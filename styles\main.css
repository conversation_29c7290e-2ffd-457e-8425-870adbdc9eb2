/*
 * Web Developer Portfolio Stylesheet
 * A modern, code-themed design for a professional web developer portfolio
 * Author: <PERSON><PERSON>
 */

/* ===== VARIABLES ===== */
:root {
  --primary-color: #7c4dff; /* Purple accent */
  --primary-dark: #651fff;
  --secondary-color: #9e9e9e;
  --dark-color: #121212; /* Dark theme background */
  --darker-color: #0a0a0a; /* Even darker sections */
  --light-color: #272727; /* Light sections in dark theme */
  --lighter-color: #333333; /* Lighter elements */
  --success-color: #00c853;
  --danger-color: #ff1744;
  --body-bg: #121212; /* Dark background */
  --text-color: #e0e0e0; /* Light text for dark background */
  --text-muted: #9e9e9e; /* Muted text */
  --border-color: #333333;
  --box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  --transition: all 0.3s ease;
  --border-radius: 8px; /* Slightly more rounded */
  --section-padding: 80px 0;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Montserrat', 'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--body-bg);
  overflow-x: hidden;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(124, 77, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(124, 77, 255, 0.05) 0%, transparent 50%);
  letter-spacing: 0.3px;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  color: var(--text-color);
  font-family: 'Montserrat', 'Poppins', sans-serif;
  font-weight: 600;
  letter-spacing: 1px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 70px;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  border: none;
}

.primary-btn {
  background-color: var(--primary-color);
  color: white;
}

.primary-btn:hover {
  background-color: var(--primary-dark);
  color: white;
}

.secondary-btn {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.secondary-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.highlight {
  color: var(--primary-color);
}

/* ===== HEADER ===== */
header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(18, 18, 18, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  font-family: 'Montserrat', 'Fira Code', 'Roboto Mono', monospace;
  letter-spacing: 1px;
}

.code-tag {
  color: var(--primary-color);
  font-weight: 500;
}

nav ul {
  display: flex;
}

nav ul li {
  margin-left: 30px;
}

nav ul li a {
  color: var(--text-color);
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
  gap: 5px;
  font-family: 'Montserrat', 'Poppins', sans-serif;
  letter-spacing: 0.5px;
}

nav ul li a i {
  font-size: 0.9rem;
  color: var(--primary-color);
}

nav ul li a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition);
}

nav ul li a:hover::after,
nav ul li a.active::after {
  width: 100%;
}

.hamburger {
  display: none;
  cursor: pointer;
}

.hamburger span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: var(--dark-color);
  margin: 5px 0;
  transition: var(--transition);
}

/* ===== HERO SECTION ===== */
.hero {
  height: 100vh;
  display: flex;
  align-items: center;
  background-color: var(--dark-color);
  background-image:
    radial-gradient(circle at 25% 25%, rgba(124, 77, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(124, 77, 255, 0.05) 0%, transparent 50%);
  background-size: cover;
  background-position: center;
  padding-top: 80px;
  position: relative;
  overflow: hidden;
}

.hero-content {
  max-width: 800px;
  position: relative;
  z-index: 2;
  color: var(--text-color);
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  font-family: 'Montserrat', sans-serif;
  letter-spacing: 1px;
}

.hero-content h2 {
  font-size: 2rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  color: var(--primary-color);
  font-family: 'Raleway', sans-serif;
  letter-spacing: 0.5px;
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--text-muted);
  max-width: 600px;
  line-height: 1.8;
}

.hero-shapes .shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(124, 77, 255, 0.1);
  animation: float 15s infinite ease-in-out;
}

.hero-shapes .shape-1 {
  width: 300px;
  height: 300px;
  top: 20%;
  right: 10%;
  animation-delay: 0s;
}

.hero-shapes .shape-2 {
  width: 200px;
  height: 200px;
  bottom: 15%;
  right: 20%;
  animation-delay: 3s;
}

.hero-shapes .shape-3 {
  width: 150px;
  height: 150px;
  bottom: 25%;
  left: 10%;
  animation-delay: 6s;
}

.hero-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(124, 77, 255, 0.1);
  animation: float 15s infinite ease-in-out;
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 20%;
  right: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  bottom: 15%;
  right: 20%;
  animation-delay: 3s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 25%;
  left: 10%;
  animation-delay: 6s;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

.cta-buttons {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--section-padding);
  background-color: var(--light-color);
}

.about-content {
  display: flex;
  align-items: center;
  gap: 50px;
}

.about-image {
  flex: 1;
}

.about-image img {
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

.about-text {
  flex: 1;
}

.about-text h3 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--primary-color);
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
}

.about-text p {
  margin-bottom: 20px;
  color: var(--text-muted);
  font-size: 1.05rem;
  line-height: 1.8;
}

.personal-info {
  margin: 30px 0;
  background-color: var(--darker-color);
  padding: 25px;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.info-item {
  margin-bottom: 15px;
  display: flex;
}

.info-title {
  min-width: 120px;
  font-weight: 600;
  color: var(--primary-color);
}

.info-value {
  color: var(--text-color);
}

/* ===== SKILLS SECTION ===== */
.skills {
  padding: var(--section-padding);
}

.skills-content {
  display: flex;
  gap: 50px;
}

.skill-category {
  flex: 1;
}

.skill-category h3 {
  margin-bottom: 30px;
  text-align: center;
  font-size: 1.8rem;
  color: var(--primary-color);
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
}

.skill-item {
  margin-bottom: 25px;
  position: relative;
}

.skill-name {
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--text-color);
  font-size: 1.1rem;
  display: flex;
  justify-content: space-between;
}

.skill-bar {
  height: 10px;
  background-color: var(--border-color);
  border-radius: 5px;
  overflow: hidden;
  position: relative;
}

.skill-level {
  height: 100%;
  background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
  border-radius: 5px;
  position: relative;
  transition: width 1s ease-in-out;
}

.skill-percent {
  position: absolute;
  right: 0;
  top: 0;
  color: var(--primary-color);
  font-weight: 600;
}

/* ===== PROJECTS SECTION ===== */
.projects {
  padding: var(--section-padding);
  background-color: var(--dark-color);
}

.projects-filter {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-btn {
  padding: 10px 25px;
  background-color: var(--darker-color);
  border: 1px solid var(--border-color);
  border-radius: 30px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  color: var(--text-color);
  font-family: 'Montserrat', sans-serif;
  letter-spacing: 0.5px;
}

.filter-btn:hover,
.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(124, 77, 255, 0.4);
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
}

.project-card {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.6);
  border-color: var(--primary-color);
}

.project-image {
  height: 200px;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-info {
  padding: 25px;
}

.project-info h3 {
  margin-bottom: 15px;
  font-size: 1.5rem;
  color: var(--primary-color);
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
}

.project-info p {
  color: var(--text-muted);
  margin-bottom: 20px;
  line-height: 1.7;
  font-size: 0.95rem;
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.project-tags span {
  background-color: var(--darker-color);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.project-links {
  display: flex;
  gap: 20px;
  margin-top: 25px;
}

.project-links a {
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-color);
  font-weight: 500;
  transition: var(--transition);
}

.project-links a:hover {
  color: white;
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--section-padding);
  background-color: var(--light-color);
}

.contact-content {
  display: flex;
  gap: 50px;
}

.contact-info {
  flex: 1;
  background-color: var(--darker-color);
  padding: 40px;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  position: relative;
  overflow: hidden;
}

.contact-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
}

.contact-info h3 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--primary-color);
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
}

.contact-info > p {
  margin-bottom: 30px;
  color: var(--text-muted);
  line-height: 1.7;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;
  padding: 15px;
  background-color: rgba(124, 77, 255, 0.05);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.contact-item:hover {
  transform: translateX(5px);
  background-color: rgba(124, 77, 255, 0.1);
}

.contact-item i {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-right: 15px;
  margin-top: 5px;
}

.contact-item h4 {
  margin-bottom: 5px;
  color: var(--text-color);
  font-weight: 600;
}

.contact-item p {
  color: var(--text-muted);
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 30px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background-color: var(--dark-color);
  border-radius: 50%;
  color: var(--primary-color);
  font-size: 1.3rem;
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.social-links a:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(124, 77, 255, 0.3);
}

.contact-form {
  flex: 1;
}

.contact-form h3 {
  font-size: 1.8rem;
  margin-bottom: 25px;
  color: var(--primary-color);
  font-family: 'Raleway', sans-serif;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 15px;
  background-color: var(--darker-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: 1rem;
  transition: var(--transition);
  color: var(--text-color);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(124, 77, 255, 0.2);
}

.form-group textarea {
  height: 150px;
  resize: vertical;
}

.contact-form .btn {
  margin-top: 10px;
  padding: 15px 30px;
}

/* ===== FOOTER ===== */
footer {
  background-color: var(--darker-color);
  color: var(--text-muted);
  padding: 30px 0;
  border-top: 1px solid var(--border-color);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.footer-content p {
  font-size: 0.95rem;
  letter-spacing: 0.5px;
}

.footer-links {
  display: flex;
  gap: 25px;
}

.footer-links a {
  color: var(--text-muted);
  font-size: 0.95rem;
  transition: var(--transition);
  position: relative;
  padding: 5px 0;
}

.footer-links a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-links a:hover::after {
  width: 100%;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 992px) {
  .about-content,
  .skills-content,
  .contact-content {
    flex-direction: column;
  }

  .hero-content h1 {
    font-size: 3rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .section-title {
    font-size: 2.2rem;
  }
}

@media (max-width: 768px) {
  nav {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: white;
    transition: var(--transition);
    z-index: 999;
  }

  nav.active {
    left: 0;
  }

  nav ul {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  nav ul li {
    margin: 15px 0;
  }

  .hamburger {
    display: block;
  }

  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-content h2 {
    font-size: 1.5rem;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
}

@media (max-width: 576px) {
  .section-title {
    font-size: 2rem;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-content h2 {
    font-size: 1.3rem;
  }

  .about-image {
    text-align: center;
  }

  .about-image img {
    max-width: 250px;
  }

  .contact-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .contact-item i {
    margin-bottom: 10px;
  }
}
