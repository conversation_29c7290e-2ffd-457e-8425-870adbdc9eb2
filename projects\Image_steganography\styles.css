body {
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-color: #0a1929; /* Fallback color */
    color: #fff;
}

.card {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
    background-color: rgba(25, 42, 86, 0.8);
    border: 1px solid rgba(0, 162, 255, 0.2);
    backdrop-filter: blur(10px);
}

.card-header {
    background-color: rgba(0, 123, 255, 0.3);
    border-bottom: 1px solid rgba(0, 162, 255, 0.2);
}

.form-control, .form-check-input {
    background-color: rgba(30, 50, 100, 0.6);
    border: 1px solid rgba(0, 162, 255, 0.3);
    color: #fff;
}

.form-control:focus {
    background-color: rgba(40, 60, 120, 0.7);
    color: #fff;
    border-color: rgba(0, 162, 255, 0.5);
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0a58ca;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-success {
    background-color: #198754;
    border-color: #146c43;
}

.btn-success:hover {
    background-color: #157347;
    border-color: #146c43;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(10, 25, 41, 0.85);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

/* Add a glowing effect to the spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
    border: 0.25em solid rgba(0, 162, 255, 0.2);
    border-right-color: #0d6efd;
    animation: spinner-border 0.75s linear infinite;
    box-shadow: 0 0 15px rgba(0, 123, 255, 0.5);
}

#imagePreview, #stegoImagePreview {
    max-height: 300px;
    border: 1px solid rgba(0, 162, 255, 0.3);
    box-shadow: 0 0 15px rgba(0, 123, 255, 0.3);
    border-radius: 5px;
}

.alert {
    border-left: 4px solid #0d6efd;
    background-color: rgba(25, 42, 86, 0.8);
    color: #fff;
    border: 1px solid rgba(0, 162, 255, 0.2);
}

.alert p {
    color: rgba(255, 255, 255, 0.9);
}

.alert strong {
    color: #fff;
    font-weight: 700;
}

.nav-tabs {
    border-bottom: 1px solid rgba(0, 162, 255, 0.3);
}

.nav-tabs .nav-link {
    color: rgba(255, 255, 255, 0.7);
    border: none;
}

.nav-tabs .nav-link:hover {
    color: #fff;
    border: none;
    border-bottom: 2px solid rgba(0, 162, 255, 0.5);
}

.nav-tabs .nav-link.active {
    color: #fff;
    background-color: rgba(0, 123, 255, 0.2);
    border: none;
    border-bottom: 2px solid #0d6efd;
}

h1 {
    color: #fff !important;
    text-shadow: 0 0 10px rgba(0, 123, 255, 0.7), 0 0 20px rgba(0, 123, 255, 0.5);
    font-weight: 700;
    letter-spacing: 1px;
    margin-bottom: 1.5rem;
}

h5, h6 {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Make all form labels white */
.form-label, .form-check-label {
    color: #fff;
}

/* Make text-muted more visible on dark background */
.text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

.container {
    padding-top: 20px;
    padding-bottom: 40px;
}

.text-info {
    color: rgba(0, 162, 255, 0.8) !important;
    text-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
    font-size: 1.1rem;
    letter-spacing: 0.5px;
}

.border {
    border-color: rgba(0, 162, 255, 0.2) !important;
}

.bg-light {
    background-color: rgba(30, 50, 100, 0.4) !important;
}

.extracted-text-container {
    background-color: rgba(30, 50, 100, 0.4);
    color: #fff;
}

pre {
    color: #fff;
    white-space: pre-wrap;
    word-break: break-word;
}
