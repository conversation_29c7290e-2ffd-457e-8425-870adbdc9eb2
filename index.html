<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Essential Meta Tags -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON><PERSON> - Web Developer Portfolio showcasing full-stack development skills and projects">
    <meta name="keywords" content="web developer, full stack developer, HTML, CSS, JavaScript, React, Node.js, portfolio">
    <meta name="author" content="G.Man<PERSON>">

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="<PERSON><PERSON> | Web Developer">
    <meta property="og:description" content="Full Stack Web Developer specializing in creating responsive and dynamic web applications">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://manohargurrampati.com">
    <meta property="og:image" content="assets/profile.jpg">

    <!-- Theme Color for Browser -->
    <meta name="theme-color" content="#4285f4">

    <!-- Title -->
    <title><PERSON><PERSON> | Web Developer</title>

    <!-- Favicon -->
    <link rel="icon" href="assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/apple-touch-icon.png">

    <!-- Google Fonts - Popular for Web Development -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Raleway:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Styles -->

    <!-- Preload Critical Resources -->
    <link rel="preload" href="scripts/main.js" as="script">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <h1 class="logo">G.<span class="highlight">Manohar</span></h1>
            <nav>
                <ul>
                    <li><a href="#home"><i class="fas fa-home"></i> Home</a></li>
                    <li><a href="#about"><i class="fas fa-user-alt"></i> About</a></li>
                    <li><a href="#skills"><i class="fas fa-code"></i> Skills</a></li>
                    <li><a href="#projects"><i class="fas fa-laptop-code"></i> Projects</a></li>
                    <li><a href="#contact"><i class="fas fa-paper-plane"></i> Contact</a></li>
                </ul>
            </nav>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Hello, I'm <span class="highlight">G.Manohar Reddy</span></h1>
                <h2>Full Stack Developer</h2>
                <p>Specializing in creating responsive and dynamic web applications with HTML, CSS, JavaScript and React.</p>
                <p>Ready to code amazing projects and bring your ideas to life.</p>
                <div class="cta-buttons">
                    <a href="#projects" class="btn primary-btn"><i class="fas fa-code"></i> View My Work</a>
                    <a href="#contact" class="btn secondary-btn"><i class="fas fa-paper-plane"></i> Contact Me</a>
                </div>
            </div>
        </div>
        <div class="hero-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-image">
                    <img src="assets\profile2.jpg" alt="Profile Picture">
                </div>
                <div class="about-text">
                    <h3>Web Developer & Designer</h3>
                    <p>Hello! I'm a passionate professional in web development with 2+ years of experience. I've worked on various projects that have helped me develop a strong skill set in HTML, CSS, JavaScript and React.</p>
                    <p>I'm dedicated to delivering high-quality work and becoming the best version of myself. I'm constantly learning and improving my skills to stay at the forefront of industry trends.</p>
                    <div class="personal-info">
                        <div class="info-item">
                            <span class="info-title">Name:</span>
                            <span class="info-value">G.Manohar Reddy</span>
                        </div>
                        <div class="info-item">
                            <span class="info-title">Email:</span>
                            <span class="info-value"><EMAIL></span>
                        </div>
                        <div class="info-item">
                            <span class="info-title">Location:</span>
                            <span class="info-value">Hyderabad, India</span>
                        </div>
                    </div>
                    <a href="assets/resume.pdf" class="btn primary-btn" download><i class="fas fa-file-download"></i> Download Resume</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title">My Skills</h2>
            <div class="skills-content">
                <div class="skill-category">
                    <h3>Technical Skills</h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <div class="skill-name">HTML/CSS</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 90%"></div>
                            </div>
                            <div class="skill-percent">90%</div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">JavaScript</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 85%"></div>
                            </div>
                            <div class="skill-percent">85%</div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">React</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 75%"></div>
                            </div>
                            <div class="skill-percent">75%</div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Node.js</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 75%"></div>
                            </div>
                            <div class="skill-percent">75%</div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Java</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 90%"></div>
                            </div>
                            <div class="skill-percent">90%</div>
                        </div>
                    </div>
                </div>
                <div class="skill-category">
                    <h3>Soft Skills</h3>
                    <div class="skill-items">
                        <div class="skill-item">
                            <div class="skill-name">Communication</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 95%"></div>
                            </div>
                            <div class="skill-percent">95%</div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Problem Solving</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 90%"></div>
                            </div>
                            <div class="skill-percent">90%</div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Teamwork</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 85%"></div>
                            </div>
                            <div class="skill-percent">85%</div>
                        </div>
                        <div class="skill-item">
                            <div class="skill-name">Adaptability</div>
                            <div class="skill-bar">
                                <div class="skill-level" style="width: 80%"></div>
                            </div>
                            <div class="skill-percent">80%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title">My Projects</h2>
            <div class="projects-filter">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="web">Web Development</button>
                <button class="filter-btn" data-filter="cyber">Cyber Security</button>
            </div>
            <div class="projects-grid">
                <!-- Image Steganography Project -->
                <div class="project-card" data-category="web cyber">
                    <div class="project-image">
                        <img src="assets\image-steganography.jpeg" alt="Image Steganography" onerror="this.src='https://via.placeholder.com/350x250?text=Image+Steganography'">
                    </div>
                    <div class="project-info">
                        <h3>Image Steganography</h3>
                        <p>A web application that allows users to hide secret messages within images using steganography techniques. Built with HTML, CSS, and JavaScript, featuring encryption capabilities and secure message extraction.</p>
                        <div class="project-tags">
                            <span>HTML</span>
                            <span>CSS</span>
                            <span>JavaScript</span>
                            <span>Cryptography</span>
                        </div>
                        <div class="project-links">
                            <a href="projects\Image_steganography\index.html" target="_blank"><i class="fas fa-external-link-alt"></i> Live Demo</a>
                            <a href="https://github.com/ManoharGurrampati/Project_CS/tree/main/Img_STG-project" target="_blank"><i class="fab fa-github"></i> Source Code</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <p>Feel free to reach out to me for any questions or opportunities. I'm always open to discussing new projects, creative ideas or opportunities to be part of your vision.</p>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h4>Phone</h4>
                            <p>+91 6302890151</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h4>Location</h4>
                            <p>Hyderabad, India</p>
                        </div>
                    </div>
                    <div class="social-links">
                        <a href="https://www.linkedin.com/in/manohar-gurrampati-a25749280/" target="_blank"><i class="fab fa-linkedin"></i></a>
                        <a href="https://github.com/ManoharGurrampati" target="_blank"><i class="fab fa-github"></i></a>
                    </div>
                </div>
                <div class="contact-form">
                    <h3>Send Me a Message</h3>
                    <form id="contactForm">
                        <div class="form-group">
                            <input type="text" id="name" name="name" placeholder="Your Name" required>
                        </div>
                        <div class="form-group">
                            <input type="email" id="email" name="email" placeholder="Your Email" required>
                        </div>
                        <div class="form-group">
                            <input type="text" id="subject" name="subject" placeholder="Subject" required>
                        </div>
                        <div class="form-group">
                            <textarea id="message" name="message" placeholder="Your Message" required></textarea>
                        </div>
                        <button type="submit" class="btn primary-btn"><i class="fas fa-paper-plane"></i> Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2023 Gurrampati Manohar Reddy. All Rights Reserved.</p>
                <div class="footer-links">
                    <a href="#home">Home</a>
                    <a href="#about">About</a>
                    <a href="#skills">Skills</a>
                    <a href="#projects">Projects</a>
                    <a href="#contact">Contact</a>
                </div>
            </div>
        </div>
    </footer>



    <!-- Main JavaScript -->
    <script src="scripts/main.js"></script>
</body>
</html>
