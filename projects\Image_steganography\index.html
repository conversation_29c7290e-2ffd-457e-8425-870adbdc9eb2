<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Steganography</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h1 class="text-center">Image Steganography</h1>
        <p class="text-center text-info mb-4">Hide your secrets in plain sight</p>

        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="embed-tab" data-bs-toggle="tab" data-bs-target="#embed" type="button" role="tab" aria-controls="embed" aria-selected="true">Embed Data</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="extract-tab" data-bs-toggle="tab" data-bs-target="#extract" type="button" role="tab" aria-controls="extract" aria-selected="false">Extract Data</button>
            </li>
        </ul>

        <div class="tab-content" id="myTabContent">
            <!-- Embed Data Tab -->
            <div class="tab-pane fade show active" id="embed" role="tabpanel" aria-labelledby="embed-tab">
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Upload Image</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="imageInput" class="form-label">Select an image:</label>
                                    <input class="form-control" type="file" id="imageInput" accept="image/*">
                                </div>
                                <div id="imagePreviewContainer" class="text-center d-none">
                                    <img id="imagePreview" class="img-fluid mb-3" alt="Image Preview">
                                    <p id="imageInfo" class="text-muted"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Data to Hide</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="dataType" id="textOption" value="text" checked>
                                        <label class="form-check-label" for="textOption">Text</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="dataType" id="fileOption" value="file">
                                        <label class="form-check-label" for="fileOption">File</label>
                                    </div>
                                </div>

                                <div id="textInputContainer">
                                    <div class="mb-3">
                                        <label for="textToHide" class="form-label">Enter text to hide:</label>
                                        <textarea class="form-control" id="textToHide" rows="5" placeholder="Type your secret message here..."></textarea>
                                    </div>
                                </div>

                                <div id="fileInputContainer" class="d-none">
                                    <div class="mb-3">
                                        <label for="fileToHide" class="form-label">Select a file to hide:</label>
                                        <input class="form-control" type="file" id="fileToHide">
                                    </div>
                                    <p id="fileInfo" class="text-muted"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button id="embedButton" class="btn btn-primary" disabled>Embed Data</button>
                        <button id="downloadButton" class="btn btn-success ms-2 d-none">Download Image</button>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h5>How it works:</h5>
                            <p>This application uses the LSB (Least Significant Bit) steganography technique to hide data within an image. The process modifies the least significant bits of the image's pixel values, making changes that are imperceptible to the human eye.</p>
                            <p><strong>Note:</strong> The capacity for hiding data depends on the image size. Larger images can store more data.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Extract Data Tab -->
            <div class="tab-pane fade" id="extract" role="tabpanel" aria-labelledby="extract-tab">
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Upload Steganographic Image</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="stegoImageInput" class="form-label">Select an image with hidden data:</label>
                                    <input class="form-control" type="file" id="stegoImageInput" accept="image/*">
                                </div>
                                <div id="stegoImagePreviewContainer" class="text-center d-none">
                                    <img id="stegoImagePreview" class="img-fluid mb-3" alt="Stego Image Preview">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Extracted Data</h5>
                            </div>
                            <div class="card-body">
                                <button id="extractButton" class="btn btn-primary mb-3" disabled>Extract Data</button>

                                <div id="extractedDataContainer" class="d-none">
                                    <div id="extractedTextContainer">
                                        <h6>Extracted Text:</h6>
                                        <div class="border p-3 mb-3 extracted-text-container">
                                            <pre id="extractedText" class="mb-0"></pre>
                                        </div>
                                    </div>

                                    <div id="extractedFileContainer" class="d-none">
                                        <h6>Extracted File:</h6>
                                        <p id="extractedFileInfo"></p>
                                        <button id="downloadExtractedFile" class="btn btn-success">Download File</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay d-none">
            <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="text-light mt-2" id="loadingText">Processing...</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
