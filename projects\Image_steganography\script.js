document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements - Embed Tab
    const imageInput = document.getElementById('imageInput');
    const imagePreview = document.getElementById('imagePreview');
    const imagePreviewContainer = document.getElementById('imagePreviewContainer');
    const imageInfo = document.getElementById('imageInfo');
    const textOption = document.getElementById('textOption');
    const fileOption = document.getElementById('fileOption');
    const textInputContainer = document.getElementById('textInputContainer');
    const fileInputContainer = document.getElementById('fileInputContainer');
    const textToHide = document.getElementById('textToHide');
    const fileToHide = document.getElementById('fileToHide');
    const fileInfo = document.getElementById('fileInfo');
    const embedButton = document.getElementById('embedButton');
    const downloadButton = document.getElementById('downloadButton');

    // DOM Elements - Extract Tab
    const stegoImageInput = document.getElementById('stegoImageInput');
    const stegoImagePreview = document.getElementById('stegoImagePreview');
    const stegoImagePreviewContainer = document.getElementById('stegoImagePreviewContainer');
    const extractButton = document.getElementById('extractButton');
    const extractedDataContainer = document.getElementById('extractedDataContainer');
    const extractedTextContainer = document.getElementById('extractedTextContainer');
    const extractedFileContainer = document.getElementById('extractedFileContainer');
    const extractedText = document.getElementById('extractedText');
    const extractedFileInfo = document.getElementById('extractedFileInfo');
    const downloadExtractedFile = document.getElementById('downloadExtractedFile');

    // Loading Overlay
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingText = document.getElementById('loadingText');

    // Variables
    let originalImage = null;
    let stegoImage = null;
    let extractedData = null;
    let extractedFileName = 'extracted_file';
    let extractedFileType = 'application/octet-stream';

    // Event Listeners
    imageInput.addEventListener('change', handleImageUpload);
    stegoImageInput.addEventListener('change', handleStegoImageUpload);
    textOption.addEventListener('change', toggleDataInputType);
    fileOption.addEventListener('change', toggleDataInputType);
    fileToHide.addEventListener('change', handleFileToHideChange);
    embedButton.addEventListener('click', embedData);
    extractButton.addEventListener('click', extractData);
    downloadButton.addEventListener('click', downloadStegoImage);
    downloadExtractedFile.addEventListener('click', downloadExtractedFileData);

    // Check if inputs have values to enable/disable buttons
    textToHide.addEventListener('input', checkEmbedInputs);

    // Functions
    function handleImageUpload(e) {
        const file = e.target.files[0];
        if (file && file.type.match('image.*')) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = new Image();
                img.onload = function() {
                    imagePreview.src = img.src;
                    imagePreviewContainer.classList.remove('d-none');
                    imageInfo.textContent = `${img.width}x${img.height} pixels`;
                    originalImage = img;
                    checkEmbedInputs();
                };
                img.src = event.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    function handleStegoImageUpload(e) {
        const file = e.target.files[0];
        if (file && file.type.match('image.*')) {
            const reader = new FileReader();
            reader.onload = function(event) {
                const img = new Image();
                img.onload = function() {
                    stegoImagePreview.src = img.src;
                    stegoImagePreviewContainer.classList.remove('d-none');
                    stegoImage = img;
                    extractButton.disabled = false;
                    extractedDataContainer.classList.add('d-none');
                };
                img.src = event.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    function toggleDataInputType() {
        if (textOption.checked) {
            textInputContainer.classList.remove('d-none');
            fileInputContainer.classList.add('d-none');
        } else {
            textInputContainer.classList.add('d-none');
            fileInputContainer.classList.remove('d-none');
        }
        checkEmbedInputs();
    }

    function handleFileToHideChange(e) {
        const file = e.target.files[0];
        if (file) {
            fileInfo.textContent = `File: ${file.name} (${formatFileSize(file.size)})`;
            checkEmbedInputs();
        }
    }

    function checkEmbedInputs() {
        if (!originalImage) {
            embedButton.disabled = true;
            return;
        }

        if (textOption.checked) {
            embedButton.disabled = textToHide.value.trim() === '';
        } else {
            embedButton.disabled = !fileToHide.files || fileToHide.files.length === 0;
        }
    }

    function showLoading(message = 'Processing...') {
        loadingText.textContent = message;
        loadingOverlay.classList.remove('d-none');
    }

    function hideLoading() {
        loadingOverlay.classList.add('d-none');
    }

    function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' bytes';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
        else return (bytes / 1048576).toFixed(2) + ' MB';
    }

    // Steganography Functions

    function embedData() {
        showLoading('Embedding data into image...');

        // Use setTimeout to allow the loading overlay to appear
        setTimeout(() => {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = originalImage.width;
                canvas.height = originalImage.height;
                ctx.drawImage(originalImage, 0, 0);

                // Get image data
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const pixels = imageData.data;

                // Prepare data to hide
                let dataToHide;
                let dataType;

                if (textOption.checked) {
                    dataToHide = textToHide.value;
                    dataType = 'text';
                } else {
                    const file = fileToHide.files[0];
                    const reader = new FileReader();

                    // We need to handle file reading asynchronously
                    reader.onload = function(event) {
                        const fileData = event.target.result;
                        const fileName = file.name;
                        const fileType = file.type || 'application/octet-stream';

                        // Create metadata for the file
                        const metadata = JSON.stringify({
                            type: 'file',
                            name: fileName,
                            mime: fileType
                        });

                        // Convert metadata to binary
                        const metadataBinary = stringToBinary(metadata);

                        // Convert file data to binary
                        const fileDataBinary = arrayBufferToBinary(fileData);

                        // Combine metadata and file data with a separator
                        const dataBinary = metadataBinary + '1111111111111111' + fileDataBinary;

                        // Check if data can fit in the image
                        const maxBits = (pixels.length / 4) * 3; // 3 bits per pixel (R,G,B)
                        if (dataBinary.length > maxBits) {
                            alert('The file is too large to hide in this image. Please use a larger image or a smaller file.');
                            hideLoading();
                            return;
                        }

                        // Embed the binary data into the image
                        embedBinaryData(pixels, dataBinary);

                        // Update the image with the embedded data
                        ctx.putImageData(imageData, 0, 0);

                        // Show the result
                        downloadButton.href = canvas.toDataURL('image/png');
                        downloadButton.classList.remove('d-none');
                        hideLoading();
                    };

                    reader.readAsArrayBuffer(file);
                    return; // Exit the function as we're handling the file reading asynchronously
                }

                // For text data
                // Create metadata for text
                const metadata = JSON.stringify({
                    type: 'text'
                });

                // Convert metadata to binary
                const metadataBinary = stringToBinary(metadata);

                // Convert text to binary
                const textBinary = stringToBinary(dataToHide);

                // Combine metadata and text with a separator
                const dataBinary = metadataBinary + '1111111111111111' + textBinary;

                // Check if data can fit in the image
                const maxBits = (pixels.length / 4) * 3; // 3 bits per pixel (R,G,B)
                if (dataBinary.length > maxBits) {
                    alert('The text is too large to hide in this image. Please use a larger image or less text.');
                    hideLoading();
                    return;
                }

                // Embed the binary data into the image
                embedBinaryData(pixels, dataBinary);

                // Update the image with the embedded data
                ctx.putImageData(imageData, 0, 0);

                // Convert canvas to blob and create download link
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    downloadButton.onclick = function() {
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'stego_image.png';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    };
                    downloadButton.classList.remove('d-none');
                    hideLoading();
                }, 'image/png');

            } catch (error) {
                console.error('Error embedding data:', error);
                alert('An error occurred while embedding data: ' + error.message);
                hideLoading();
            }
        }, 100);
    }

    function extractData() {
        showLoading('Extracting data from image...');

        setTimeout(() => {
            try {
                if (!stegoImage) {
                    throw new Error('No image selected');
                }

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = stegoImage.width;
                canvas.height = stegoImage.height;
                ctx.drawImage(stegoImage, 0, 0);

                // Get image data
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const pixels = imageData.data;

                // Simple check if this is likely a steganographic image
                if (pixels.length < 128) { // Need at least 32 pixels for header
                    throw new Error('Image is too small to contain hidden data');
                }

                // Extract binary data from the image
                const extractedBinary = extractBinaryData(pixels);

                // Check if we got valid binary data
                if (!extractedBinary || extractedBinary.length < 32) {
                    throw new Error('Could not extract valid data from image');
                }

                // Try different approaches to extract data
                try {
                    // First approach: Look for the separator
                    const separatorIndex = extractedBinary.indexOf('1111111111111111');

                    if (separatorIndex !== -1 && separatorIndex < extractedBinary.length - 16) {
                        // We found a valid separator
                        const metadataBinary = extractedBinary.substring(0, separatorIndex);
                        const contentBinary = extractedBinary.substring(separatorIndex + 16);

                        // Convert metadata from binary to string
                        const metadataStr = binaryToString(metadataBinary);

                        // Try to parse the metadata
                        try {
                            const metadata = JSON.parse(metadataStr);

                            if (metadata.type === 'text') {
                                // Convert content from binary to string
                                const extractedTextContent = binaryToString(contentBinary);

                                // Display the extracted text
                                extractedText.textContent = extractedTextContent;
                                extractedTextContainer.classList.remove('d-none');
                                extractedFileContainer.classList.add('d-none');
                                extractedDataContainer.classList.remove('d-none');
                                hideLoading();
                                return;
                            } else if (metadata.type === 'file' && metadata.name && metadata.mime) {
                                // Convert binary to array buffer
                                const fileData = binaryToArrayBuffer(contentBinary);

                                // Create a blob from the array buffer
                                const blob = new Blob([fileData], { type: metadata.mime });

                                // Store the extracted file data
                                extractedData = blob;
                                extractedFileName = metadata.name;
                                extractedFileType = metadata.mime;

                                // Display file info
                                extractedFileInfo.textContent = `File: ${metadata.name} (${formatFileSize(fileData.byteLength)})`;
                                extractedTextContainer.classList.add('d-none');
                                extractedFileContainer.classList.remove('d-none');
                                extractedDataContainer.classList.remove('d-none');
                                hideLoading();
                                return;
                            }
                        } catch (jsonError) {
                            console.warn('Failed to parse metadata JSON, trying alternative approach');
                        }
                    }

                    // Second approach: Try to interpret the entire data as text
                    const extractedTextContent = binaryToString(extractedBinary);

                    // Check if the extracted text looks valid (contains printable ASCII characters)
                    const printableChars = extractedTextContent.replace(/[^\x20-\x7E]/g, '');
                    if (printableChars.length > extractedTextContent.length * 0.5) {
                        // More than 50% printable characters, likely text
                        extractedText.textContent = printableChars;
                        extractedTextContainer.classList.remove('d-none');
                        extractedFileContainer.classList.add('d-none');
                        extractedDataContainer.classList.remove('d-none');
                        hideLoading();
                        return;
                    }

                    // If we get here, we couldn't extract meaningful data
                    throw new Error('Could not extract meaningful data from this image');

                } catch (innerError) {
                    console.error('Inner extraction error:', innerError);
                    throw new Error('Failed to extract data: ' + innerError.message);
                }

            } catch (error) {
                console.error('Error extracting data:', error);
                alert('An error occurred while extracting data: ' + error.message);
                hideLoading();
            }
        }, 100);
    }

    function downloadStegoImage() {
        // This function is now handled directly in the embedData function
    }

    function downloadExtractedFileData() {
        if (extractedData) {
            const url = URL.createObjectURL(extractedData);
            const a = document.createElement('a');
            a.href = url;
            a.download = extractedFileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }

    // Helper Functions for Binary Conversion

    function stringToBinary(str) {
        let binary = '';
        for (let i = 0; i < str.length; i++) {
            const charCode = str.charCodeAt(i);
            const bin = charCode.toString(2);
            binary += '0'.repeat(8 - bin.length) + bin; // Ensure 8 bits per character
        }
        return binary;
    }

    function binaryToString(binary) {
        let str = '';
        for (let i = 0; i < binary.length; i += 8) {
            const byte = binary.substr(i, 8);
            if (byte.length === 8) {
                const charCode = parseInt(byte, 2);
                str += String.fromCharCode(charCode);
            }
        }
        return str;
    }

    function arrayBufferToBinary(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.length; i++) {
            const bin = bytes[i].toString(2);
            binary += '0'.repeat(8 - bin.length) + bin; // Ensure 8 bits per byte
        }
        return binary;
    }

    function binaryToArrayBuffer(binary) {
        const bytes = new Uint8Array(Math.ceil(binary.length / 8));
        for (let i = 0; i < bytes.length; i++) {
            const byteStr = binary.substr(i * 8, 8) || '0'.repeat(8);
            bytes[i] = parseInt(byteStr, 2);
        }
        return bytes.buffer;
    }

    // LSB Steganography Implementation

    function embedBinaryData(pixels, binary) {
        // First, embed the length of the binary data (32 bits)
        const lengthBinary = binary.length.toString(2).padStart(32, '0');

        // Embed length
        for (let i = 0; i < 32; i++) {
            const pixelIndex = i * 4; // RGBA (4 bytes per pixel)
            const bit = parseInt(lengthBinary[i]);

            // Modify the least significant bit of the red channel
            pixels[pixelIndex] = (pixels[pixelIndex] & 0xFE) | bit;
        }

        // Then embed the actual data
        for (let i = 0; i < binary.length; i++) {
            const bit = parseInt(binary[i]);
            const pixelIndex = (i + 32) * 4; // Start after the length data

            if (pixelIndex >= pixels.length) break; // Prevent overflow

            // Modify the least significant bit of the red channel
            pixels[pixelIndex] = (pixels[pixelIndex] & 0xFE) | bit;
        }
    }

    function extractBinaryData(pixels) {
        try {
            // First, extract the length (32 bits)
            let lengthBinary = '';
            for (let i = 0; i < 32; i++) {
                const pixelIndex = i * 4;
                if (pixelIndex >= pixels.length) {
                    throw new Error('Image too small to contain steganographic data');
                }
                const bit = pixels[pixelIndex] & 0x01;
                lengthBinary += bit;
            }

            let dataLength = parseInt(lengthBinary, 2);

            // Sanity check for data length
            const maxPossibleLength = Math.floor((pixels.length / 4) * 3); // Maximum possible bits
            if (dataLength <= 0 || dataLength > maxPossibleLength || isNaN(dataLength)) {
                console.warn('Invalid data length detected:', dataLength, 'Setting to safe default');
                // Set a reasonable default - extract 1/10 of the possible data to be safe
                dataLength = Math.floor(maxPossibleLength / 10);
            }

            // Then extract the actual data
            let binary = '';
            for (let i = 0; i < dataLength; i++) {
                const pixelIndex = (i + 32) * 4; // Start after the length data

                if (pixelIndex >= pixels.length) break; // Prevent overflow

                const bit = pixels[pixelIndex] & 0x01;
                binary += bit;
            }

            return binary;
        } catch (error) {
            console.error('Error in extractBinaryData:', error);
            // Return a minimal valid binary string that can be processed
            return '0'.repeat(64);
        }
    }
});
